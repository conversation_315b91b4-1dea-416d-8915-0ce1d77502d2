<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Selector Component</title>
    <style>
        /* Base styles matching your existing design */
        .language-selector-container {
            background: #ffffff;
            border-radius: 12px;
            padding: 16px 20px;
            margin: 12px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            border: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 16px;
            transition: all 0.2s ease;
        }

        .language-selector-container:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }

        .language-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .language-icon svg {
            width: 24px;
            height: 24px;
            fill: white;
        }

        .language-content {
            flex: 1;
            min-width: 0;
        }

        .language-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 4px 0;
        }

        .language-description {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
            line-height: 1.4;
        }

        .language-dropdown {
            position: relative;
        }

        .language-select-button {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
            transition: all 0.2s ease;
        }

        .language-select-button:hover {
            border-color: #6366f1;
            background: #f1f5f9;
        }

        .language-select-button:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .language-select-button[aria-expanded="true"] {
            border-color: #6366f1;
            background: #f1f5f9;
        }

        .dropdown-arrow {
            margin-left: auto;
            transition: transform 0.2s ease;
        }

        .language-select-button[aria-expanded="true"] .dropdown-arrow {
            transform: rotate(180deg);
        }

        .language-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            margin-top: 4px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-8px);
            transition: all 0.2s ease;
        }

        .language-options.open {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .language-option {
            padding: 12px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }

        .language-option:hover,
        .language-option:focus {
            background: #f8fafc;
            outline: none;
        }

        .language-option.selected {
            background: #eff6ff;
            color: #1d4ed8;
            font-weight: 500;
        }

        .language-option:first-child {
            border-radius: 6px 6px 0 0;
        }

        .language-option:last-child {
            border-radius: 0 0 6px 6px;
        }

        .language-flag {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        /* Dark mode styles */
        .dark-mode .language-selector-container {
            background: #374151;
            border-color: #4b5563;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .dark-mode .language-title {
            color: #f9fafb;
        }

        .dark-mode .language-description {
            color: #d1d5db;
        }

        .dark-mode .language-select-button {
            background: #4b5563;
            border-color: #6b7280;
            color: #f9fafb;
        }

        .dark-mode .language-select-button:hover,
        .dark-mode .language-select-button[aria-expanded="true"] {
            background: #374151;
            border-color: #8b5cf6;
        }

        .dark-mode .language-options {
            background: #374151;
            border-color: #6b7280;
        }

        .dark-mode .language-option {
            color: #f9fafb;
        }

        .dark-mode .language-option:hover,
        .dark-mode .language-option:focus {
            background: #4b5563;
        }

        .dark-mode .language-option.selected {
            background: #312e81;
            color: #a5b4fc;
        }
    </style>
</head>
<body>
    <!-- Language Selector Component -->
    <div class="language-selector-container">
        <div class="language-icon">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12.87 15.07l-2.54-2.51.03-.03A17.52 17.52 0 0014.07 6H17V4h-7V2H8v2H1v2h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>
            </svg>
        </div>
        <div class="language-content">
            <h3 class="language-title" id="language-title">Interface Language</h3>
            <p class="language-description" id="language-description">Choose your preferred language for the accessibility menu</p>
        </div>
        <div class="language-dropdown">
            <button 
                class="language-select-button" 
                id="language-selector"
                role="combobox"
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="language-title"
                aria-describedby="language-description"
            >
                <span class="language-flag" id="selected-flag">🇺🇸</span>
                <span id="selected-language">English</span>
                <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                    <path d="M6 8L2 4h8L6 8z"/>
                </svg>
            </button>
            <div class="language-options" id="language-options" role="listbox" aria-labelledby="language-title">
                <button class="language-option selected" role="option" data-lang="en" data-flag="🇺🇸">
                    <span class="language-flag">🇺🇸</span>
                    <span>English</span>
                </button>
                <button class="language-option" role="option" data-lang="fr" data-flag="🇫🇷">
                    <span class="language-flag">🇫🇷</span>
                    <span>Français</span>
                </button>
                <button class="language-option" role="option" data-lang="ar" data-flag="🇸🇦">
                    <span class="language-flag">🇸🇦</span>
                    <span>العربية</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Demo: Toggle dark mode for testing -->
    <button onclick="document.body.classList.toggle('dark-mode')" style="margin: 20px; padding: 10px;">
        Toggle Dark Mode
    </button>

    <script>
        class LanguageSelector {
            constructor() {
                this.button = document.getElementById('language-selector');
                this.options = document.getElementById('language-options');
                this.selectedFlag = document.getElementById('selected-flag');
                this.selectedLanguage = document.getElementById('selected-language');
                this.currentIndex = 0;
                this.isOpen = false;

                this.languages = [
                    { code: 'en', name: 'English', flag: '🇺🇸' },
                    { code: 'fr', name: 'Français', flag: '🇫🇷' },
                    { code: 'ar', name: 'العربية', flag: '🇸🇦' }
                ];

                this.init();
            }

            init() {
                this.loadSavedLanguage();
                this.bindEvents();
            }

            loadSavedLanguage() {
                const savedLang = localStorage.getItem('accessibility-menu-language') || 'en';
                const langIndex = this.languages.findIndex(lang => lang.code === savedLang);
                if (langIndex !== -1) {
                    this.selectLanguage(langIndex, false);
                }
            }

            bindEvents() {
                // Button click
                this.button.addEventListener('click', () => this.toggleDropdown());

                // Keyboard navigation
                this.button.addEventListener('keydown', (e) => this.handleButtonKeydown(e));
                this.options.addEventListener('keydown', (e) => this.handleOptionsKeydown(e));

                // Option selection
                this.options.addEventListener('click', (e) => {
                    if (e.target.closest('.language-option')) {
                        const option = e.target.closest('.language-option');
                        const langCode = option.dataset.lang;
                        const langIndex = this.languages.findIndex(lang => lang.code === langCode);
                        this.selectLanguage(langIndex);
                    }
                });

                // Close on outside click
                document.addEventListener('click', (e) => {
                    if (!e.target.closest('.language-dropdown')) {
                        this.closeDropdown();
                    }
                });

                // Close on escape
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && this.isOpen) {
                        this.closeDropdown();
                        this.button.focus();
                    }
                });
            }

            toggleDropdown() {
                if (this.isOpen) {
                    this.closeDropdown();
                } else {
                    this.openDropdown();
                }
            }

            openDropdown() {
                this.isOpen = true;
                this.button.setAttribute('aria-expanded', 'true');
                this.options.classList.add('open');

                // Focus first option
                const firstOption = this.options.querySelector('.language-option');
                if (firstOption) {
                    firstOption.focus();
                }
            }

            closeDropdown() {
                this.isOpen = false;
                this.button.setAttribute('aria-expanded', 'false');
                this.options.classList.remove('open');
            }

            handleButtonKeydown(e) {
                switch (e.key) {
                    case 'Enter':
                    case ' ':
                    case 'ArrowDown':
                        e.preventDefault();
                        this.openDropdown();
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        this.openDropdown();
                        // Focus last option
                        const lastOption = this.options.querySelector('.language-option:last-child');
                        if (lastOption) lastOption.focus();
                        break;
                }
            }

            handleOptionsKeydown(e) {
                const options = Array.from(this.options.querySelectorAll('.language-option'));
                const currentIndex = options.indexOf(document.activeElement);

                switch (e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        const nextIndex = (currentIndex + 1) % options.length;
                        options[nextIndex].focus();
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        const prevIndex = currentIndex === 0 ? options.length - 1 : currentIndex - 1;
                        options[prevIndex].focus();
                        break;
                    case 'Enter':
                    case ' ':
                        e.preventDefault();
                        const langCode = document.activeElement.dataset.lang;
                        const langIndex = this.languages.findIndex(lang => lang.code === langCode);
                        this.selectLanguage(langIndex);
                        break;
                    case 'Home':
                        e.preventDefault();
                        options[0].focus();
                        break;
                    case 'End':
                        e.preventDefault();
                        options[options.length - 1].focus();
                        break;
                }
            }

            selectLanguage(index, save = true) {
                const language = this.languages[index];
                if (!language) return;

                // Update UI
                this.selectedFlag.textContent = language.flag;
                this.selectedLanguage.textContent = language.name;

                // Update selected state
                this.options.querySelectorAll('.language-option').forEach((option, i) => {
                    option.classList.toggle('selected', i === index);
                });

                // Save preference
                if (save) {
                    localStorage.setItem('accessibility-menu-language', language.code);
                    console.log('Language set to:', language.code);
                }

                // Close dropdown
                this.closeDropdown();
                this.button.focus();

                this.currentIndex = index;
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new LanguageSelector();
        });
    </script>
</body>
</html>
