---
type: "always_apply"
---

## 🎯 1. Design Quality
- The UI must be **modern**, **clean**, and **aesthetically pleasing** — on par with top-selling plugins on ThemeForest.
- Use **visually balanced layouts**, clear spacing, elegant shadows, rounded corners, and harmonious color palettes.
- All icons must be **professionally designed** (avoid basic emojis). Use expressive, consistent, high-quality icon sets.
- Avoid generic or repetitive UI elements. Each section should feel **intentional and crafted**.

---

## 📱 2. Responsive & Scalable
- The plugin must be **fully responsive**: all components should look and behave perfectly on desktop, tablet, and mobile.
- Avoid hardcoded sizes or rigid layouts. Think in terms of **scalable design**.

---

## ⚙️ 3. UX Behavior
- Prioritize **clarity, smooth interactions**, and accessibility.
- Use **smooth transitions** (not abrupt changes) when elements appear/disappear or are toggled.
- Provide **instant visual feedback** on all user actions (e.g., button clicks, theme selection).
- Avoid clutter. Each section should feel **light and easy to use**.

---

## 💼 4. Business-Oriented Thinking
- Every feature, style, and behavior should aim to **increase product appeal and usability**, making it easy to **convert visitors into buyers**.
- Think like a ThemeForest reviewer: the product must feel premium, complete, and polished — no placeholders, no obvious defaults.
- Always optimize for **real-world use**: don't add features just for testing or visual filler.

---

## 🧪 5. Testing & Debugging
- Avoid including test scripts, demo content, or debug files in production components unless explicitly requested.
- Assume the testing will be handled externally.

---

## ✅ 6. Maintainability
- Your code should be **clean, modular, and scalable** — easy to maintain and extend in the future.
- Follow best practices (naming, structure, comments) even in small components.
